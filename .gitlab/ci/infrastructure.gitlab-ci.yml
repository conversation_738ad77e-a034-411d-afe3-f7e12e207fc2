include:
  - local: .gitlab/ci/includes/shared.gitlab-ci.yml

.if-environment-deployment-commit: &if-environment-deployment-commit
  if: '$CI_COMMIT_REF_PROTECTED == "true"'

.plan-terraform: &plan-terraform
  - tofu init -var-file=$TFVARS_FILE
  - tofu validate -var-file=$TFVARS_FILE
  - tofu plan -var-file=$TFVARS_FILE -out=tfplan-$CI_COMMIT_SHORT_SHA

.apply-terraform: &apply-terraform
  - tofu init -var-file=$TFVARS_FILE
  - tofu plan -var-file=$TFVARS_FILE -out=tfplan-$CI_COMMIT_SHORT_SHA
  - tofu apply -auto-approve -json tfplan-$CI_COMMIT_SHORT_SHA

.terraform-base: &terraform-base
  - cd infrastructure

.terraform-without-state:
  before_script:
    - !reference [.common-base, before_script]
    - *terraform-base

.terraform-with-state:
  before_script:
    - !reference [.gcp-auth-base, before_script]
    - *terraform-base

infrastructure:check:
  stage: lint
  extends:
    - .terraform-without-state
  script:
    - tofu fmt -check -recursive

infrastructure:initialize-and-plan:
  stage: build
  extends:
    - .terraform-with-state
  script:
    - *plan-terraform
  artifacts:
    paths:
      - infrastructure/tfplan-$CI_COMMIT_SHORT_SHA
    when: on_success
  
infrastructure:apply:
  stage: deploy
  interruptible: false
  extends: 
    - .terraform-with-state
  script:
    - *apply-terraform
  rules:
    - <<: *if-environment-deployment-commit
  artifacts:
    paths:
      - infrastructure/tfplan-$CI_COMMIT_SHORT_SHA
    when: on_success
