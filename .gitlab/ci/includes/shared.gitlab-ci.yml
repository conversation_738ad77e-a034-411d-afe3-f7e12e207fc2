# script for running mise-en-place to install dependencies
.mise-en-place: &mise-en-place
  - echo "Installing curl..."
  - apt update && apt install -y curl
  - echo "Configuring mise"
  - export PATH=$PATH:/root/.local/share/mise/bin:/root/.local/share/mise/shims:/root/.local/bin
  - curl -k https://mise.run | sh
  - /root/.local/bin/mise install --yes
  - mv /root/.local/bin/mise /usr/local/bin
  - mise reshim
  - echo "Mise config complete!"

# script for authenticating with gcloud
.gcloud-login: &gcloud-login
  - echo "Authenticating with G<PERSON>"
  - echo ${DEPLOYMENT_SERVICE_ACCOUNT}
  - echo ${DEPLOYMENT_SERVICE_ACCOUNT} > ${CI_PROJECT_DIR}/gcp-key.json
  - gcloud auth activate-service-account --key-file=${CI_PROJECT_DIR}/gcp-key.json
  - export GOOGLE_APPLICATION_CREDENTIALS=${CI_PROJECT_DIR}/gcp-key.json
  - echo "gCloud config complete!"

# common base job for all jobs
.common-base:
  before_script:
    - *mise-en-place

# base job for jobs requiring GCP authentication
.gcp-auth-base:
  before_script:
    - *mise-en-place
    - *gcloud-login
  after_script:
    # remove the key.json file after the job completes
    - rm -f ${CI_PROJECT_DIR}/key.json
