#!/bin/bash
#Tableau Server Logs Export and  Cleanup Script
# Load arguments as bash variables
TABLEAU_ADMIN_USERNAME="$1" # "tableau-admin"
TABLEAU_ADMIN_PASSWORD_SECRET="$2" # "tableau-server-admin-password". Tableau admin secret is stored in respective project secret manager.
TABLEAU_ARTIFACT_BUCKET="$3" # For Ex: "ict-d-tableau-artifacts" is the bucket name in ict-d-tableau project. Look for bucket name in respective project where the nodes exist.

if [ "$#" != 3 ]; then
    printf "Usage: $0 <tableau_admin_username> <tableau_admin_password_secret> <tableau_artifact_bucket>\n"
    printf "\tExpected: 3\n\tActual: $#\n"
    exit 1
fi

# define Variables
DATE=`date +%Y-%m-%d`
TIMESTAMP=`date '+%Y-%m-%d %H:%M:%S'`
copy_logs="yes"

# Archive log
GCS_BACKUP_PATH="gs://$TABLEAU_ARTIFACT_BUCKET/logs/"

#how many days do you want to keep the archived logs
logs_days="7"

#name of the logs file, append timestamp for the file name.
log_name="logs"

environment_file=/etc/opt/tableau/tableau_server/environment.bash
# Load Environmnet variables
source /etc/profile.d/tableau_server.sh

load_environment_file() {
  if [[ -f $environment_file ]]; then
    source /etc/opt/tableau/tableau_server/environment.bash
    env_file_exists=1
  fi
}
if [ "$(whoami)" != "$TABLEAU_ADMIN_USERNAME" ]; then

  printf "INFO - Re-running as the Tableau admin user: $TABLEAU_ADMIN_USERNAME...\n"
  
  exec sudo -iu "$TABLEAU_ADMIN_USERNAME"
fi

if [ "$#" -eq 3 ] ; then
	# Get tsm username from command line input
	tsmuser="$1" # tsmuser="$USER"
	# Get tsm password from command line input
	tsmpassword=$(gcloud secrets versions access latest --secret="$TABLEAU_ADMIN_PASSWORD_SECRET")
	tsmparams="-u $tsmuser -p $tsmpassword"
elif [ $(echo $TABLEAU_SERVER_DATA_DIR_VERSION | cut -d. -f1) -ge 20192 ]  && (id -nG | grep -q tsmadmin || [ ${EUID} -eq 0 ]) ; then 
	# 2019.2 workflow. If running as tsmadmin member or root, do not set userinfo
	declare tsmparams
fi

# path to logs archive folder
log_path=$(tsm configuration get -k basefilepath.log_archive $tsmparams)
TIMESTAMP=`date '+%Y-%m-%d %H:%M:%S'`
echo $TIMESTAMP "The path for storing log archives is $log_path" 

# Log files count eligible for cleaning up
file_count=$(find $log_path -type f -name '*.zip' -mtime +$log_days | wc -l)
if [ $file_count -eq 0 ]; then 
	TIMESTAMP=`date '+%Y-%m-%d %H:%M:%S'`
	echo $TIMESTAMP $file_count found, skipping...	
else echo $TIMESTAMP $file_count found, deleting...
	#remove log archives older than the specified number of days
	find $log_path -type f -name '*.zip' -mtime +$log_days -exec rm {} \;
	TIMESTAMP=`date '+%Y-%m-%d %H:%M:%S'`
	echo $TIMESTAMP "Cleaning up completed."		
fi
#stopping tsm 
echo $TIMESTAMP": Initate TSM Stop."
tsm stop
echo $TIMESTAMP": TSM Stop Completed."
# archive current logs to tmp folder
TIMESTAMP=`date '+%Y-%m-%d %H:%M:%S'`
echo $TIMESTAMP": Archiving Tableau Server Logs."
tsm maintenance ziplogs -f logs-$DATE.zip $tsmparams
if [ "$copy_logs" == "yes" ]; then
	TIMESTAMP=`date '+%Y-%m-%d %H:%M:%S'`
	echo $TIMESTAMP": Copying Logs to GCP Storage Bucket."
	gsutil cp $log_path/$log_name-$DATE.zip $GCS_BACKUP_PATH 
fi

# cleanup logs
TIMESTAMP=`date '+%Y-%m-%d %H:%M:%S'`
echo $TIMESTAMP": Initaite Tableau Logs Cleanup."
tsm maintenance cleanup -a $tsmparams
TIMESTAMP=`date '+%Y-%m-%d %H:%M:%S'`
echo $TIMESTAMP": Logs Cleanup Completed."
TIMESTAMP=`date '+%Y-%m-%d %H:%M:%S'`
echo $TIMESTAMP": Initiate TSM start."
tsm start
TIMESTAMP=`date '+%Y-%m-%d %H:%M:%S'`
echo $TIMESTAMP": TSM Start Completed."