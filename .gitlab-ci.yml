stages:
  - lint
  - build
  - deploy

default:
  image: $GITLAB_RUNNER_IMAGE
  interruptible: true # allows the use of the "auto cancel redundant pipelines" feature
  timeout: 60m
  tags:
    - gke-runners-dematic
  retry:
    max: 2
    when: runner_system_failure
    exit_codes: 137

.if-draft-mr: &if-draft-mr
  if: '$CI_MERGE_REQUEST_DRAFT == "true"'

.if-dev-pipeline: &if-dev-pipeline
  if: '$CI_COMMIT_BRANCH == "dev" || $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'

.if-stage-pipeline: &if-stage-pipeline
  if: '$CI_COMMIT_BRANCH == "stage" || $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "stage"'

.if-prod-pipeline: &if-prod-pipeline
  if: '$CI_COMMIT_BRANCH == "main" || $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "main"'

.if-protected-branch-pipeline: &if-protected-branch-pipeline
  if: '$CI_MERGE_REQUEST_TARGET_BRANCH_PROTECTED == "true" || $CI_COMMIT_REF_PROTECTED == "true"'

.if-non-protected-commit-branch-has-open-mr: &if-non-protected-commit-branch-has-open-mr
  if: '$CI_COMMIT_REF_PROTECTED == "false" && $CI_OPEN_MERGE_REQUESTS'

workflow:
  rules:
    # ---------------------------------------------------------------------------------------------
    # Don't run anything for draft MRs
    # ---------------------------------------------------------------------------------------------
    - <<: *if-draft-mr
      when: never
    # ---------------------------------------------------------------------------------------------
    # Run for protected (environment) branches.
    # ---------------------------------------------------------------------------------------------
    - <<: *if-protected-branch-pipeline
    # ---------------------------------------------------------------------------------------------
    # Don't run if a non-protected commit branch has an open MR (prevent duplicate runs on MR updates)
    # ---------------------------------------------------------------------------------------------
    - <<: *if-non-protected-commit-branch-has-open-mr
      when: never
    # ---------------------------------------------------------------------------------------------
    # Don't run under any other circumstances
    # ---------------------------------------------------------------------------------------------
    - when: never

variables:
  GITLAB_RUNNER_IMAGE: "us-docker.pkg.dev/ict-o-registry/ict-o-registry/gitlab-runner:latest"
  DOCKER_DRIVER: "overlay2"
  DOCKER_TLS_CERTDIR: ""
  DIND_BUILD_IMAGE: "us-docker.pkg.dev/ict-o-registry/ict-o-registry/ops-dind:20"

  TF_VAR_cost_center: "550164"

  TF_VAR_git_commit_sha: "$CI_COMMIT_SHA"
  TF_VAR_gitlab_group_name: "tableau"
  TF_VAR_gitlab_project_shortname: "$CI_PROJECT_NAME"
  
  TF_VAR_identity_config_file_path: "$CI_PROJECT_DIR/artifacts/identity_config.json"
  TF_VAR_registration_file_path: "$CI_PROJECT_DIR/artifacts/registration.json"

include:
  - local: .gitlab/ci/environments/dev.gitlab-ci.yml
    rules:
      - <<: *if-dev-pipeline
  - local: .gitlab/ci/environments/stage.gitlab-ci.yml
    rules:
      - <<: *if-stage-pipeline
  - local: .gitlab/ci/environments/prod.gitlab-ci.yml
    rules:
      - <<: *if-prod-pipeline
  - local: .gitlab/ci/infrastructure.gitlab-ci.yml
