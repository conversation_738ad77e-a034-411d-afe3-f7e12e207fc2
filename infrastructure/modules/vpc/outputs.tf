output "app_subnet_cidr" {
  description = "The CIDR range of the subnet created for the Tableau Server app nodes."
  value = one([
    for subnet in module.network.subnets : subnet.ip_cidr_range
    if subnet.name == local.app_subnet_name
  ])
}

output "app_subnet_self_link" {
  description = "The self-link of the subnet created for the Tableau Server app nodes."
  value = one([
    for subnet in module.network.subnets : subnet.self_link
    if subnet.name == local.app_subnet_name
  ])
}

output "cloud_router_self_link" {
  description = "The self-link of the cloud router created for the Tableau Server stack."
  value       = module.backend_router.router.self_link
}

output "network_self_link" {
  value       = module.network.network_self_link
  description = "The self_link of the VPC network created for the Tableau Server stack."
}

output "serverless_connector_id" {
  description = "The ID of the serverless connector created for the Tableau Server stack, in the form \"projects/{{project}}/locations/{{region}}/connectors/{{name}}\"."
  value       = google_vpc_access_connector.main.id
}

output "serverless_connector_subnet_cidr" {
  description = "The CIDR range of the subnet created for the Tableau Server app nodes."
  value = one([
    for subnet in module.network.subnets : subnet.ip_cidr_range
    if subnet.name == local.serverless_connector_subnet_name
  ])
}

output "serverless_connector_subnet_self_link" {
  description = "The self-link of the subnet created for the Tableau Server app nodes."
  value = one([
    for subnet in module.network.subnets : subnet.self_link
    if subnet.name == local.serverless_connector_subnet_name
  ])
}
