module "network" {
  source  = "terraform-google-modules/network/google"
  version = local.network_module_version

  project_id   = var.project_id
  network_name = local.network_name
  routing_mode = "GLOBAL"

  auto_create_subnetworks = false

  subnets = [
    {
      subnet_name   = local.app_subnet_name
      subnet_ip     = var.app_subnet_cidr
      subnet_region = var.region
    },
    {
      subnet_name   = local.serverless_connector_subnet_name
      subnet_ip     = var.serverless_connector_subnet_cidr
      subnet_region = var.region
    }
  ]

  ingress_rules = [
    {
      name        = "allow-ingress-from-health-checks"
      description = "Allow traffic from the load balancer health checks."

      priority      = 1000
      source_ranges = var.google_health_check_ip_address_ranges
      target_tags   = [var.network_tag]

      allow = [
        {
          protocol = "tcp"
          ports    = ["80", "8850"]
        }
      ]
    },
    {
      name        = "allow-ingress-from-iap"
      description = "Allow ingress traffic from Cloud IAP."

      priority      = 1000
      source_ranges = var.google_iap_ip_address_ranges
      target_tags   = [var.network_tag]

      allow = [
        {
          protocol = "tcp"
          ports    = ["22"]
        }
      ]
    },
    {
      name        = "allow-ingress-from-hosts"
      description = "Allow hosts to communicate with each other."

      priority      = 1000
      source_ranges = [var.app_subnet_cidr]
      target_tags   = [var.network_tag]

      allow = [
        {
          protocol = "tcp"
          ports = [
            "80",
            "443",
            "3000",
            "8000-9000",
            "27000-27009"
          ]
        }
      ]
    }
  ]
}

module "backend_router" {
  source  = "terraform-google-modules/cloud-router/google"
  version = local.cloud_router_module_version

  name        = local.cloud_router_name
  description = "Cloud router for allowing internet access for Tableau Server backend nodes during configuration."

  project = var.project_id
  network = module.network.network_self_link
  region  = var.region

  nats = [
    {
      name                               = local.cloud_nat_name
      nat_ip_allocate_option             = "AUTO_ONLY"
      source_subnetwork_ip_ranges_to_nat = "ALL_SUBNETWORKS_ALL_IP_RANGES"
    }
  ]
}

resource "google_vpc_access_connector" "main" {
  name = "${var.resource_prefix}-con"

  machine_type = "e2-micro" # default value

  min_instances = 2 # default value
  max_instances = 7

  subnet {
    name = local.serverless_connector_subnet_name
  }
}
