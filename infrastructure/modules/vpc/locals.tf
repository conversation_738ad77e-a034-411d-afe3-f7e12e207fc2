locals {
  # remove the dash for use in resource names
  region_flattened = replace(var.region, "-", "")

  cloud_router_module_version = "~> 6.0"
  cloud_nat_module_version    = "~> 5.3"
  network_module_version      = "~> 9.1"

  network_name                     = "${var.resource_prefix}-network"
  app_subnet_name                  = "${var.resource_prefix}-${local.region_flattened}-app"
  serverless_connector_subnet_name = "${var.resource_prefix}-${local.region_flattened}-serverless-connector"

  cloud_nat_name    = "${var.resource_prefix}-backend-nat"
  cloud_router_name = "${var.resource_prefix}-backend-router"
}
