variable "app_subnet_cidr" {
  description = "The CIDR range to use for the Tableau Server app subnet (generally a /24)."
  type        = string

  validation {
    condition     = can(regex("^[0-9]+\\.[0-9]+\\.[0-9]+\\.[0-9]+/[0-9]+$", var.app_subnet_cidr))
    error_message = "Subnet CIDR must be in the format 'x.x.x.x/y'."
  }
}

variable "google_health_check_ip_address_ranges" {
  description = "The list of IP addresses to use for the health check."
  type        = list(string)
}

variable "google_iap_ip_address_ranges" {
  description = "The origin IP ranges for GCP IAP traffic, for use with the IAP firewall rule."
  type        = list(string)
}

variable "network_tag" {
  description = "The network tag used for the Tableau Server nodes."
  type        = string
}

variable "project_id" {
  description = "The project_id of the project to create the network in."
  type        = string
}

variable "region" {
  description = "The region to create the network in."
  type        = string
}

variable "resource_prefix" {
  description = "The prefix to use for resource naming."
  type        = string
  default     = "tableau-server"
}

variable "serverless_connector_subnet_cidr" {
  description = "The CIDR range to use for the serverless connector subnet (generally a /28)."
  type        = string

  validation {
    condition     = can(regex("^[0-9]+\\.[0-9]+\\.[0-9]+\\.[0-9]+/[0-9]+$", var.serverless_connector_subnet_cidr))
    error_message = "Subnet CIDR must be in the format 'x.x.x.x/y'."
  }
}
