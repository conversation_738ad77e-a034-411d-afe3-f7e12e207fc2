#!/bin/bash

# Startup script for Tableau Server cluster nodes. The script creates the Tableau Admin user and group, and installs Tableau Server.
# TSM initialization and cluster configuration is performed post initialization.

# ---- Validate input parameters ------ #

if [ "$#" -ne 5 ]; then
    printf "ERROR - Usage: $0 <tableau_admin_username> <tableau_admin_password_secret> <tableau_admin_group> <tableau_artifact_bucket> <tableau_version>\n"
    printf "\tExpected: 5 arguments\n\tActual: $# argument(s)\n"
    exit 1
fi

# ------------------------------------- #
# ---- Set arguments ------------------ #

TABLEAU_ADMIN_USERNAME=$1
TABLEAU_ADMIN_PASSWORD_SECRET=$2
TABLEAU_ADMIN_GROUP=$3
TABLEAU_ARTIFACT_BUCKET=$4
TABLEAU_VERSION=$5

tableau_version_dashes=$(echo $TABLEAU_VERSION | sed 's/\./-/g')
tableau_installer_filename="tableau-server-${tableau_version_dashes}_amd64.deb"
tableau_installer_uri="gs://${TABLEAU_ARTIFACT_BUCKET}/${tableau_installer_filename}"
installer_download_path="/tmp/${tableau_installer_filename}"

# ------------------------------------- #
# ---- Output error line number ------- #

trap 'printf "ERROR - An error occurred during script execution on line $LINENO. Exiting...\n"/ exit 1' ERR

# ------------------------------------- #
# ---- Create Tableau admin user ------ #

printf "INFO - Beginning Tableau admin user creation...\n"

sudo groupadd $TABLEAU_ADMIN_GROUP

printf "INFO - Tableau admin group created.\n"
printf "INFO - Reading Tableau admin user name from Secret Manager...\n"

TABLEAU_ADMIN_PASSWORD=$(gcloud secrets versions access latest --secret=$TABLEAU_ADMIN_PASSWORD_SECRET)

if [ $? -ne 0 ]; then
    printf "ERROR - Failed to read the Tableau admin password from Secret Manager. Exiting...\n"
    exit 1
fi

printf "INFO - Tableau admin password retrieved.\n"
printf "INFO - Creating Tableau admin user...\n"

sudo useradd -m -s /bin/bash -G sudo,$TABLEAU_ADMIN_GROUP $TABLEAU_ADMIN_USERNAME
echo "$TABLEAU_ADMIN_USERNAME:$TABLEAU_ADMIN_PASSWORD" | sudo chpasswd
echo "$TABLEAU_ADMIN_USERNAME ALL=(ALL) NOPASSWD:ALL" | sudo tee /etc/sudoers.d/$TABLEAU_ADMIN_USERNAME
sudo chmod 0440 /etc/sudoers.d/$TABLEAU_ADMIN_USERNAME

printf "INFO - Tableau admin user created.\n"
printf "INFO - Tableau admin user creation complete.\n"

# ------------------------------------- #
# ---- Install Tableau Server --------- #

printf "INFO - Beginning Tableau installation...\n"

printf "INFO - Downloading Tableau Server installer...\n"

gsutil cp $tableau_installer_uri $installer_download_path

if [ $? -ne 0 ]; then
    printf "ERROR - Failed to download the Tableau installer. Exiting...\n"
    exit 1
fi

printf "INFO - Tableau installer download complete.\n"
printf "INFO - Installing Tableau...\n"

sudo DEBIAN_FRONTEND=noninteractive NEEDRESTART_MODE=a apt-get update --yes -qq
sudo DEBIAN_FRONTEND=noninteractive NEEDRESTART_MODE=a apt-get install $installer_download_path --yes -qq
sudo DEBIAN_FRONTEND=noninteractive NEEDRESTART_MODE=a apt-get clean --yes -qq

if [ $? -ne 0 ]; then
    printf "ERROR - Failed to install Tableau Server. Exiting...\n"
    exit 1
fi

printf "INFO - Tableau installation complete.\n"
printf "INFO - Cleaning up...\n"

rm $installer_download_path

printf "INFO - Cleanup complete.\n"
printf "INFO - Tableau installation process complete.\n"
