output "backend_service_id" {
  description = "The ID of the backend service for the Tableau Server cluster."
  value       = google_compute_backend_service.main.id
}

output "primary_node_private_ip" {
  description = "The private IP address of the primary Tableau Server node."
  value       = google_compute_instance.primary_node.network_interface.0.network_ip
}

output "primary_node_name" {
  description = "The name of the primary Tableau Server node."
  value       = google_compute_instance.primary_node.name
}

output "service_account_email" {
  description = "The email address of the service account used by the Tableau server VMs."
  value       = google_service_account.main.email
}

output "total_node_count" {
  description = "The number of Tableau Server nodes."
  value       = google_compute_instance_group.app.size
}
