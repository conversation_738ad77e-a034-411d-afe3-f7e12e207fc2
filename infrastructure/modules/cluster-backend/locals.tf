locals {
  common_node_labels = merge(
    var.common_node_labels,
    {
      tableau_version = replace(var.tableau_version, ".", "_"),
    },
  )

  local_resource_prefix  = "${var.resource_prefix}-cluster"
  ops_project_id         = "ict-ops-management"
  tableau_admin_group    = "local-tableau-admins"
  service_account_scopes = ["cloud-platform"]

  service_account_roles = [
    "roles/compute.networkUser",
    "roles/compute.instanceAdmin",
    "roles/logging.logWriter",
    "roles/monitoring.metricWriter",
    "roles/secretmanager.admin",
    "roles/servicemanagement.reporter",
    "roles/storage.objectUser",
  ]

  service_account_ops_project_roles = [
    "roles/storage.objectAdmin",
    "roles/secretmanager.secretAccessor",
  ]

  tableau_installer_filename = "tableau-server-${replace(var.tableau_version, ".", "-")}_amd64.deb"
}
