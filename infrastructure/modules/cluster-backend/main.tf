resource "google_service_account" "main" {
  account_id = "${var.resource_prefix}-node-sa"
}

resource "google_project_iam_member" "tableau_project" {
  for_each = toset(local.service_account_roles)
  project  = var.project_id

  role   = each.value
  member = "serviceAccount:${google_service_account.main.email}"
}

resource "google_project_iam_member" "ops_project" {
  for_each = toset(local.service_account_ops_project_roles)
  project  = local.ops_project_id

  role   = each.value
  member = "serviceAccount:${google_service_account.main.email}"
}

resource "google_compute_instance" "primary_node" {
  name         = "${var.resource_prefix}-node-1"
  zone         = var.zone
  machine_type = var.primary_node_config.machine_type

  tags   = [var.network_tag]
  labels = merge(local.common_node_labels, { tableau_server_role = "primary_node" })

  allow_stopping_for_update = true
  metadata_startup_script   = <<-EOF
    #!/bin/bash

    printf "Setting up the Tableau server admin user...\n"

    tableau_admin_password=$(gcloud secrets versions access latest --secret=${var.tableau_admin_password_secret})

    if [ -z "$tableau_admin_password" ]; then
      echo "Failed to retrieve the Tableau server admin password from Secret Manager."
      exit 1
    fi
    
    sudo groupadd ${local.tableau_admin_group}
    sudo useradd -m -s /bin/bash -G sudo,${local.tableau_admin_group} ${var.tableau_admin_username}
    echo "${var.tableau_admin_username}:$tableau_admin_password" | sudo chpasswd
    echo "${var.tableau_admin_username} ALL=(ALL) NOPASSWD:ALL" | sudo tee /etc/sudoers.d/${var.tableau_admin_username}
    sudo chmod 0440 /etc/sudoers.d/${var.tableau_admin_username}

    printf "Installing the Tableau server...\n"
    
    installer_uri="gs://${var.tableau_installer_bucket}/${local.tableau_installer_filename}"
    installer_path="/tmp/${local.tableau_installer_filename}"

    gsutil cp $installer_uri $installer_path

    if [ $? -ne 0 ]; then
      echo "Failed to download the Tableau server installer from $installer_uri."
      exit 1
    fi

    sudo DEBIAN_FRONTEND=noninteractive NEEDRESTART_MODE=a apt-get update --yes -qq
    sudo DEBIAN_FRONTEND=noninteractive NEEDRESTART_MODE=a apt-get install $installer_path --yes -qq

    if [ $? -ne 0 ]; then
      echo "Failed to install the Tableau server. Check the logs for more information."
      exit 1
    fi

    sudo DEBIAN_FRONTEND=noninteractive NEEDRESTART_MODE=a apt-get clean --yes -qq

    printf "Tableau server installation complete.\n"
    sudo rm $installer_path

  EOF

  boot_disk {
    initialize_params {
      image = var.image_family
      size  = var.primary_node_config.disk_size_gb
      type  = var.primary_node_config.disk_type
    }
  }

  network_interface {
    subnetwork = var.subnet
  }

  service_account {
    email  = google_service_account.main.email
    scopes = local.service_account_scopes
  }
}

resource "google_compute_instance" "additional_nodes" {
  count = var.additional_node_config.count

  name         = "${var.resource_prefix}-node-${count.index + 2}"
  zone         = var.zone
  machine_type = var.additional_node_config.machine_type

  tags   = [var.network_tag]
  labels = merge(local.common_node_labels, { tableau_server_role = "node" })

  allow_stopping_for_update = true
  metadata_startup_script   = <<-EOF
    #!/bin/bash

    printf "Setting up the Tableau server admin user...\n"

    tableau_admin_password=$(gcloud secrets versions access latest --secret=${var.tableau_admin_password_secret})

    if [ -z "$tableau_admin_password" ]; then
      echo "Failed to retrieve the Tableau server admin password from Secret Manager."
      exit 1
    fi
    
    sudo groupadd ${local.tableau_admin_group}
    sudo useradd -m -s /bin/bash -G sudo,${local.tableau_admin_group} ${var.tableau_admin_username}
    echo "${var.tableau_admin_username}:$tableau_admin_password" | sudo chpasswd
    echo "${var.tableau_admin_username} ALL=(ALL) NOPASSWD:ALL" | sudo tee /etc/sudoers.d/${var.tableau_admin_username}
    sudo chmod 0440 /etc/sudoers.d/${var.tableau_admin_username}

    printf "Installing the Tableau server...\n"
    
    installer_uri="gs://${var.tableau_installer_bucket}/${local.tableau_installer_filename}"
    installer_path="/tmp/${local.tableau_installer_filename}"

    gsutil cp $installer_uri $installer_path

    if [ $? -ne 0 ]; then
      echo "Failed to download the Tableau server installer from $installer_uri."
      exit 1
    fi

    sudo DEBIAN_FRONTEND=noninteractive NEEDRESTART_MODE=a apt-get update --yes -qq
    sudo DEBIAN_FRONTEND=noninteractive NEEDRESTART_MODE=a apt-get install $installer_path --yes -qq

    if [ $? -ne 0 ]; then
      echo "Failed to install the Tableau server. Check the logs for more information."
      exit 1
    fi

    sudo DEBIAN_FRONTEND=noninteractive NEEDRESTART_MODE=a apt-get clean --yes -qq

    printf "Tableau server installation complete.\n"
    sudo rm $installer_path

  EOF

  boot_disk {
    initialize_params {
      image = var.image_family
      size  = var.additional_node_config.disk_size_gb
      type  = var.additional_node_config.disk_type
    }
  }

  network_interface {
    subnetwork = var.subnet
  }

  service_account {
    email  = google_service_account.main.email
    scopes = local.service_account_scopes
  }
}

resource "google_compute_instance_group" "app" {
  name        = "${var.resource_prefix}-backend"
  description = "Instance group for the Tableau server nodes."
  project     = var.project_id
  zone        = var.zone

  named_port {
    name = "http"
    port = 80
  }

  named_port {
    name = "https"
    port = 80
  }

  instances = concat(
    [google_compute_instance.primary_node.self_link],
    [for instance in google_compute_instance.additional_nodes : instance.self_link]
  )
}

resource "google_compute_health_check" "main" {
  name        = "${local.local_resource_prefix}-health-check"
  description = "TCP health check for the Tableau Server backend."

  check_interval_sec  = 15
  timeout_sec         = 10
  healthy_threshold   = 2
  unhealthy_threshold = 2

  tcp_health_check {
    port = 80
  }
}

resource "google_compute_security_policy" "app" {
  name        = "${local.local_resource_prefix}-backend-security-policy"
  description = "Security policy for the Tableau Server backend."
}

resource "google_compute_security_policy_rule" "rate_limiting" {
  description = "Rate limiting rule for the Tableau Server backend security policy."

  security_policy = google_compute_security_policy.app.name
  action          = "throttle"
  priority        = **********

  match {
    versioned_expr = "SRC_IPS_V1"

    config {
      src_ip_ranges = ["*"]
    }
  }

  rate_limit_options {
    conform_action = "allow"
    exceed_action  = "deny(403)"

    enforce_on_key_configs {
      enforce_on_key_type = "IP"
    }

    rate_limit_threshold {
      count        = 500
      interval_sec = 60
    }
  }
}

resource "google_compute_backend_service" "main" {
  name        = "${local.local_resource_prefix}-backend"
  description = "Backend service for the Tableau Server load balancer."

  protocol  = "HTTP"
  port_name = "http"

  timeout_sec   = 10
  health_checks = [google_compute_health_check.main.id]

  locality_lb_policy    = "RING_HASH"
  load_balancing_scheme = "EXTERNAL_MANAGED"
  session_affinity      = "CLIENT_IP"
  security_policy       = google_compute_security_policy.app.name

  iap {
    enabled = false
  }

  log_config {
    enable      = true
    sample_rate = 1
  }

  backend {
    group           = google_compute_instance_group.app.self_link
    balancing_mode  = "UTILIZATION"
    max_utilization = 0.8
  }
}
