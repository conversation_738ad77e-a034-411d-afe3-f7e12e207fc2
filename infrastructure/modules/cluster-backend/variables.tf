variable "additional_node_config" {
  description = "Configuration for the remaining nodes in the Tableau Server cluster."
  type = object({
    count        = number
    machine_type = string
    disk_size_gb = number
    disk_type    = string
  })

  default = {
    count        = 3
    machine_type = "e2-standard-8"
    disk_size_gb = 50
    disk_type    = "pd-ssd"
  }

  validation {
    condition     = var.additional_node_config.count >= 0
    error_message = "There must be at least 1 additional node."
  }

  validation {
    condition     = var.additional_node_config.count <= 5
    error_message = "There can be at most 5 additional nodes."
  }
}

variable "common_node_labels" {
  description = "Common labels to apply to all Tableau Server nodes."
  type        = map(string)
  default     = {}
}

variable "image_family" {
  description = "The image family to use for the Tableau Server nodes."
  type        = string
  default     = "ubuntu-2204-lts"
}

variable "network_tag" {
  description = "The network tag to apply to the Tableau Server nodes; used for applying firewall rules."
  type        = string
}

variable "primary_node_config" {
  description = "Configuration of the primary Tableau Server node."
  type = object({
    machine_type = string
    disk_size_gb = number
    disk_type    = string
  })

  default = {
    machine_type = "e2-highmem-8"
    disk_size_gb = 50
    disk_type    = "pd-ssd"
  }
}

variable "project_id" {
  description = "The GCP project ID where the Tableau Server stack will be deployed."
  type        = string
}

variable "resource_prefix" {
  description = "A prefix to apply to all Tableau Server resources."
  type        = string
  default     = "tableau-server"
}

variable "subnet" {
  description = "The self_link of the subnet created for the Tableau Server app layer. "
  type        = string
}

variable "tableau_admin_password_secret" {
  description = "The name of the secret containing the Tableau Server admin password."
  type        = string
}

variable "tableau_admin_username" {
  description = "The username of the Tableau Server admin user."
  type        = string
}

variable "tableau_installer_bucket" {
  description = "The name of the GCS bucket containing the Tableau Server installer."
  type        = string
}

variable "tableau_version" {
  description = "The version number of Tableau Server to install."
  type        = string

  validation {
    condition     = can(regex("^[0-9]+\\.[0-9]+\\.[0-9]+$", var.tableau_version))
    error_message = "Tableau version number must be in the format 'x.y.z'."
  }
}

variable "zone" {
  description = "The zone where the Tableau Server nodes and instance group will be created."
  type        = string
}
