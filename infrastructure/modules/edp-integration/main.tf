resource "google_service_account" "main" {
  account_id   = "${var.resource_prefix}-sa"
  display_name = "EDP Integration Service Account"
  project      = var.project_id
}

resource "google_project_iam_member" "tableau" {
  for_each = toset(local.service_account_roles)
  project  = var.project_id

  role   = each.value
  member = "serviceAccount:${google_service_account.main.email}"
}

resource "google_project_iam_member" "api" {
  for_each = toset(local.service_account_roles)
  project  = var.api_project_id

  role   = each.value
  member = "serviceAccount:${google_service_account.main.email}"
}

resource "google_service_account_key" "main" {
  service_account_id = google_service_account.main.name

}

resource "google_secret_manager_secret" "main" {
  secret_id = "${var.resource_prefix}-sa-key"
  replication {
    auto {
    }
  }

  depends_on = [google_service_account_key.main]
}

resource "google_secret_manager_secret_version" "main" {
  secret      = google_secret_manager_secret.main.id
  secret_data = google_service_account_key.main.private_key
}
