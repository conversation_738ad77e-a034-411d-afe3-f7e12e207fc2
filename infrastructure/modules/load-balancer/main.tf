
resource "google_compute_managed_ssl_certificate" "main" {
  name        = "${var.resource_prefix}-ssl-cert"
  description = "Managed SSL certificate for the Tableau Server load balancer."

  managed {
    domains = [var.domain]
  }
}
resource "google_compute_ssl_policy" "main" {
  name        = "${var.resource_prefix}-ssl-policy"
  description = "SSL policy for the Tableau Server load balancer."

  profile         = "MODERN"
  min_tls_version = "TLS_1_2"
}
resource "google_compute_global_address" "main" {
  name        = "${var.resource_prefix}-alb-ip"
  description = "Global IP address for the Tableau Server load balancer."
}
resource "google_compute_url_map" "main" {
  name            = "${var.resource_prefix}-gealb"
  description     = "URL map for the Tableau Server load balancer."
  default_service = var.cluster_backend_service_id

  host_rule {
    hosts        = ["*"]
    path_matcher = "tableau"
  }

  path_matcher {
    name            = "tableau"
    default_service = var.cluster_backend_service_id

    path_rule {
      paths   = ["/management", "/management/*"]
      service = var.management_backend_service_id
    }
  }

  # Test these tests after confirming the rest of the URL map logic works
  # test {
  #   service = var.cluster_backend_service_id
  #   host    = var.domain
  #   path    = "/"
  # }
  # test {
  #   service = var.management_backend_service_id
  #   host    = var.domain
  #   path    = "/management"
  # }
}
resource "google_compute_target_https_proxy" "main" {
  name        = "${var.resource_prefix}-alb-https-proxy"
  description = "HTTPS proxy for the Tableau Server load balancer."

  ssl_policy       = google_compute_ssl_policy.main.id
  ssl_certificates = [google_compute_managed_ssl_certificate.main.id]
  url_map          = google_compute_url_map.main.id
}
resource "google_compute_global_forwarding_rule" "main" {
  name                  = "${var.resource_prefix}-alb"
  description           = "Global forwarding rule for the Tableau Server load balancer."
  load_balancing_scheme = "EXTERNAL_MANAGED"
  #ip_version            = "IPV4"

  ip_address = google_compute_global_address.main.address
  target     = google_compute_target_https_proxy.main.id
  port_range = "443"
}
