resource "google_storage_bucket" "main" {
  name     = var.bucket_name
  location = "US"

  uniform_bucket_level_access = true
  force_destroy               = true
}

resource "google_storage_bucket_object" "registration_file" {
  name   = "registration.json"
  bucket = google_storage_bucket.main.name
  source = var.registration_file_path
}

resource "google_storage_bucket_object" "identity_config_file" {
  name   = "identity-store-config.json"
  bucket = google_storage_bucket.main.name
  source = var.identity_config_file_path
}
