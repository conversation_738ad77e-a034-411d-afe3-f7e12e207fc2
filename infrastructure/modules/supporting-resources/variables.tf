variable "bucket_name" {
  description = "A globally unique name to use for the Cloud Storage bucket."
  type        = string
}

variable "identity_config_file_path" {
  description = "The path to the identity store configuration file."
  type        = string
}

variable "project_id" {
  description = "The project_id of the project to create the network in."
  type        = string
}

variable "registration_file_path" {
  description = "The path to the registration file."
  type        = string
}
