resource "google_service_account" "main" {
  account_id = "${var.resource_prefix}-sa"
  project    = var.project_id
}

resource "google_project_iam_member" "tableau_project" {
  for_each = toset(local.service_account_roles)
  project  = var.project_id

  role   = each.value
  member = "serviceAccount:${google_service_account.main.email}"
}

resource "google_project_iam_member" "artifact_registry_project" {
  for_each = toset(local.service_account_artifact_project_roles)
  project  = var.artifact_registry_project

  role   = each.value
  member = "serviceAccount:${local.cloud_run_service_agent}"
}

resource "google_cloud_run_v2_service" "main" {
  name                = "ict-${local.local_resource_prefix}"
  location            = var.region
  deletion_protection = false

  ingress = "INGRESS_TRAFFIC_ALL"

  template {
    service_account = google_service_account.main.email

    containers {
      image = local.full_image_name

      resources {
        limits = {
          cpu    = var.cloud_run_cpu_limit
          memory = var.cloud_run_memory_limit
        }
      }

      env {
        name  = "ENVIRONMENT"
        value = var.environment
      }

      env {
        name  = "COST_CENTER"
        value = var.cost_center
      }

      env {
        name  = "PROJECT_ID"
        value = var.project_id
      }

      env {
        name  = "TABLEAU_NODE_IP"
        value = var.primary_node_private_ip
      }

      env {
        name  = "AUTH_DOMAIN"
        value = var.auth_domain
      }

      env {
        name  = "AUTH_AUDIENCE"
        value = var.auth_audience
      }

      env {
        name  = "SHA"
        value = var.git_commit_sha
      }
    }

    vpc_access {
      connector = var.vpc_access_connector_name
      egress    = "ALL_TRAFFIC"
    }
  }
}

resource "google_cloud_run_service_iam_binding" "main" {
  service  = google_cloud_run_v2_service.main.name
  location = var.region
  role     = "roles/run.invoker"
  members  = ["allUsers", "serviceAccount:${google_service_account.main.email}"]
}

# explicitly add the service account in case the "allUsers" binding is removed to make the API private
resource "google_cloud_run_service_iam_member" "cloudrun_service_account" {
  service  = google_cloud_run_v2_service.main.name
  location = var.region
  role     = "roles/run.invoker"
  member   = "serviceAccount:${google_service_account.main.email}"
}

resource "google_cloud_run_service_iam_member" "all_users" {
  service  = google_cloud_run_v2_service.main.name
  location = var.region
  role     = "roles/run.invoker"
  member   = "serviceAccount:${google_service_account.main.email}"
}

resource "google_compute_security_policy" "main" {
  name        = "${local.local_resource_prefix}-backend-security-policy"
  description = "Security policy for the Tableau Management API backend."

  type = "CLOUD_ARMOR"

  adaptive_protection_config {
    layer_7_ddos_defense_config {
      enable          = true
      rule_visibility = "STANDARD"
    }
  }

  advanced_options_config {
    json_parsing = "STANDARD"
    log_level    = "NORMAL"

    json_custom_config {
      content_types = []
    }
  }

  rule {
    action   = "allow"
    priority = **********

    match {
      versioned_expr = "SRC_IPS_V1"

      config {
        src_ip_ranges = ["*"]
      }
    }
  }
}

resource "google_compute_region_network_endpoint_group" "main" {
  name                  = "${local.local_resource_prefix}-neg"
  description           = "Network endpoint group for the Tableau Management API Cloud Run."
  network_endpoint_type = "SERVERLESS"
  region                = var.region

  cloud_run {
    service = google_cloud_run_v2_service.main.name
  }
}

resource "google_compute_backend_service" "main" {
  name        = "ict-${local.local_resource_prefix}-backend"
  description = "Backend service for the Tableau Management API."

  protocol  = "HTTP"
  port_name = "http"

  timeout_sec = 30

  load_balancing_scheme = "EXTERNAL_MANAGED"

  security_policy = google_compute_security_policy.main.self_link

  backend {
    group = google_compute_region_network_endpoint_group.main.self_link
  }

  iap {
    enabled = false
  }

  log_config {
    enable      = true
    sample_rate = 1.0
  }
}
