variable "artifact_registry_project" {
  description = "The project containing the Artifact Registry with the Tableau Management API docker image."
  type        = string
}

variable "artifact_registry_repository" {
  description = "The repository containing the Tableau Management API docker image."
  type        = string
}

variable "artifact_registry_root" {
  description = "The root URL of the Artifact Registry."
  type        = string
}

variable "auth_audience" {
  description = "The audience to use for authentication."
  type        = string
}

variable "auth_domain" {
  description = "The domain to use for authentication."
  type        = string
}

variable "cloud_run_cpu_limit" {
  description = "The CPU limit to use for the Cloud Run service."
  type        = string
  default     = "2"
}

variable "cloud_run_memory_limit" {
  description = "The memory limit to use for the Cloud Run service."
  type        = string
  default     = "1024Mi"
}

variable "cost_center" {
  description = "The cost center to use for the deployment."
  type        = string
}

variable "environment" {
  description = "The name of the environment targeted for the deployment."
  type        = string
}

variable "git_commit_sha" {
  description = "The SHA of the Git commit to use for the deployment."
  type        = string
}

variable "image_name" {
  description = "The name of the Docker image to use for the Cloud Run service."
  type        = string
}

variable "primary_node_private_ip" {
  description = "The private IP address of the primary node in the Tableau Server cluster."
  type        = string
}

variable "project_id" {
  description = "The ID of the project to create the management API resources in."
  type        = string
}

variable "region" {
  description = "The region to create the management API resources in."
  type        = string
}

variable "resource_prefix" {
  description = "The prefix to use for resource naming."
  type        = string
  default     = "tableau-server"
}

variable "service_name" {
  description = "The name of the management API service."
  type        = string
}

variable "target_tag" {
  description = "The release tag for the Cloud Run Docker image."
  type        = string
}

# variable "teams_notification_channel" {
#   description = "The Teams channel that will receive notifications."
#   type        = string
# }

variable "vpc_access_connector_name" {
  description = "The name of the VPC Access connector to use for the Cloud Run service, in the format \"projects/<project_id>/locations/<region>/connectors/<connector_name>."
  type        = string
}
