locals {
  # remove the dash for use in resource names
  #region_flattened = replace(var.region, "-", "")

  cloud_run_module_version = "0.13.0"

  full_image_name       = "${var.artifact_registry_root}/${var.artifact_registry_project}/${var.artifact_registry_repository}/${var.image_name}:${var.target_tag}"
  local_resource_prefix = "${var.resource_prefix}-mgmt-api"

  cloud_run_service_agent = "service-${data.google_project.artifact_registry_project.number}@serverless-robot-prod.iam.gserviceaccount.com"

  service_account_roles = [
    "roles/compute.networkUser",
    "roles/vpcaccess.user"
  ]

  service_account_artifact_project_roles = [
    "roles/artifactregistry.reader"
  ]
}
