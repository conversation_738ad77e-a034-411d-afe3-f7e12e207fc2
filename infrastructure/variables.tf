variable "additional_node_count" {
  description = "The number of additional server nodes to deploy."
  type        = number

  validation {
    condition     = var.additional_node_count >= 0
    error_message = "Additional app node count must be greater than or equal to 0."
  }
}

variable "additional_node_disk_size_gb" {
  description = "The disk size in GB to use for the additional server nodes."
  type        = number
}

variable "additional_node_disk_type" {
  description = "The disk type to use for the additional server nodes."
  type        = string
}

variable "additional_node_machine_type" {
  description = "The machine type to use for the additional server nodes."
  type        = string
  default     = "e2-highmem-8" # minimum machine spec requirements
}

variable "api_project_id" {
  description = "The ID of the API project"
  type        = string
}

variable "app_subnet_cidr" {
  description = "The CIDR range to use for the Tableau Server app subnet (generally a /24)."
  type        = string

  validation {
    condition     = can(regex("^[0-9]+\\.[0-9]+\\.[0-9]+\\.[0-9]+/[0-9]+$", var.app_subnet_cidr))
    error_message = "Subnet CIDR must be in the format 'x.x.x.x/y'."
  }
}

variable "artifact_registry_project" {
  description = "The project containing the Artifact Registry with the Tableau Management API docker image."
  type        = string
}

variable "artifact_registry_repository" {
  description = "The repository containing the Tableau Management API docker image."
  type        = string
}

variable "artifact_registry_root" {
  description = "The root URL of the Artifact Registry."
  type        = string
}

variable "auth_domain" {
  description = "The Auth0 authentication domain."
  type        = string
}

variable "auth_audience" {
  description = "The Auth0 authentication audience."
  type        = string
}

variable "cloud_run_image_name" {
  description = "The name of the Docker image to use for the Cloud Run service."
  type        = string
}

variable "cloud_run_cpu_limit" {
  description = "The CPU limit to use for the Cloud Run service."
  type        = string
  default     = "2"
}

variable "cloud_run_memory_limit" {
  description = "The memory limit to use for the Cloud Run service."
  type        = string
  default     = "1024Mi"
}

variable "cloud_run_service_name" {
  description = "The name of the Cloud Run service."
  type        = string
}

variable "cloud_run_target_tag" {
  description = "The image tag to target for the Cloud Run service container."
  type        = string
}

variable "cluster_node_image_family" {
  description = "The image family to use for the Tableau Server instances."
  type        = string
}

variable "cost_center" {
  description = "The cost center to use for the deployment."
  type        = string
}

variable "domain" {
  description = "The domain that will host the Tableau Server environment."
  type        = string
}

variable "environment" {
  description = "The environment in which to deploy the stack."
  type        = string
}

variable "git_commit_sha" {
  description = "The SHA of the Git commit to use for the deployment."
  type        = string
}

variable "gitlab_group_name" {
  description = "The name of the GitLab group containing the project. Used to generate the state file prefix."
  type        = string
}

variable "gitlab_project_shortname" {
  description = "The shortname of the GitLab project, used for configuring the GCS backend.."
  type        = string
}

variable "identity_config_file_path" {
  description = "The path to the identity store configuration file."
  type        = string
}

variable "network_tag" {
  description = "The network tag to use to apply firewall rules to the VM hosts."
  type        = string
}

variable "primary_node_disk_size_gb" {
  description = "The disk size in GB to use for the primary Tableau Server node."
  type        = number
}

variable "primary_node_disk_type" {
  description = "The disk type to use for the primary Tableau Server node."
  type        = string
}

variable "primary_node_machine_type" {
  description = "The machine type to use for the primary Tableau Server node. This instance has higher minimum machine spec requirements because certain services can only run on the initial node."
  type        = string
  default     = "e2-highmem-8" # minimum machine spec requirements
}

variable "project_id" {
  description = "The ID of the project in which to deploy the stack."
  type        = string
}

variable "region" {
  description = "The GCP region to use for the Tableau server nodes."
  type        = string
}

variable "registration_file_path" {
  description = "The path to the registration file."
  type        = string
}

variable "resource_prefix" {
  description = "The prefix to use for resource naming."
  type        = string
}

variable "serverless_connector_subnet_cidr" {
  description = "The CIDR range to use for the serverless connector subnet (generally a /28)."
  type        = string

  validation {
    condition     = can(regex("^[0-9]+\\.[0-9]+\\.[0-9]+\\.[0-9]+/[0-9]+$", var.serverless_connector_subnet_cidr))
    error_message = "Subnet CIDR must be in the format 'x.x.x.x/y'."
  }
}

variable "supporting_resource_bucket_name" {
  description = "A globally unique name to use for the Cloud Storage bucket that will host the supporting resources."
  type        = string
}

variable "tableau_admin_password_secret" {
  description = "The name of the secret containing the Tableau Server admin password."
  type        = string
}

variable "tableau_admin_username" {
  description = "The username of the Tableau Server admin user."
  type        = string
}

variable "tableau_installer_bucket" {
  description = "The name of the GCS bucket containing the Tableau Server installer."
  type        = string
}

variable "tableau_version" {
  description = "The version number of Tableau Server to install."
  type        = string

  validation {
    condition     = can(regex("^[0-9]+\\.[0-9]+\\.[0-9]+$", var.tableau_version))
    error_message = "Tableau version number must be in the format 'x.y.z'."
  }
}

variable "bigquery_scheduler_service_account_email" {
  description = "Email address of the BigQuery scheduler service account from another project that needs access to this tableau project's BigQuery resources."
  type        = string
}

variable "zone" {
  description = "The GCP zone to use for the Tableau server nodes."
  type        = string
}
