module "vpc" {
  source = "./modules/vpc"

  resource_prefix = var.resource_prefix
  network_tag     = var.network_tag

  project_id = var.project_id
  region     = var.region

  app_subnet_cidr                  = var.app_subnet_cidr
  serverless_connector_subnet_cidr = var.serverless_connector_subnet_cidr

  google_health_check_ip_address_ranges = local.google_health_check_ip_address_ranges
  google_iap_ip_address_ranges          = local.google_iap_ip_address_ranges
}

module "cluster_backend" {
  source = "./modules/cluster-backend"

  resource_prefix = var.resource_prefix

  project_id = var.project_id
  subnet     = module.vpc.app_subnet_self_link
  zone       = var.zone

  image_family    = var.cluster_node_image_family
  network_tag     = var.network_tag
  tableau_version = var.tableau_version

  tableau_admin_username        = var.tableau_admin_username
  tableau_admin_password_secret = var.tableau_admin_password_secret
  tableau_installer_bucket      = var.tableau_installer_bucket

  primary_node_config = {
    machine_type = var.primary_node_machine_type
    disk_size_gb = var.primary_node_disk_size_gb
    disk_type    = var.primary_node_disk_type
  }

  additional_node_config = {
    count        = var.additional_node_count
    machine_type = var.additional_node_machine_type
    disk_size_gb = var.additional_node_disk_size_gb
    disk_type    = var.additional_node_disk_type
  }
}

module "management_backend" {
  source = "./modules/management-backend"

  project_id = var.project_id
  region     = var.region

  cost_center     = var.cost_center
  environment     = var.environment
  git_commit_sha  = var.git_commit_sha
  resource_prefix = var.resource_prefix


  artifact_registry_project    = var.artifact_registry_project
  artifact_registry_repository = var.artifact_registry_repository
  artifact_registry_root       = var.artifact_registry_root

  auth_audience = var.auth_audience
  auth_domain   = var.auth_domain

  primary_node_private_ip   = module.cluster_backend.primary_node_private_ip
  vpc_access_connector_name = module.vpc.serverless_connector_id

  service_name           = var.cloud_run_service_name
  cloud_run_cpu_limit    = var.cloud_run_cpu_limit
  cloud_run_memory_limit = var.cloud_run_memory_limit
  image_name             = var.cloud_run_image_name
  target_tag             = var.cloud_run_target_tag

}

module "load_balancer" {
  source = "./modules/load-balancer"

  resource_prefix = var.resource_prefix

  cluster_backend_service_id    = module.cluster_backend.backend_service_id
  management_backend_service_id = module.management_backend.backend_service_id
  domain                        = var.domain
}

module "supporting_resources" {
  source = "./modules/supporting-resources"

  project_id                = var.project_id
  bucket_name               = var.supporting_resource_bucket_name
  registration_file_path    = var.registration_file_path
  identity_config_file_path = var.identity_config_file_path
}

module "edp_integration" {
  source = "./modules/edp-integration"

  api_project_id = var.api_project_id

  project_id      = var.project_id
  resource_prefix = "edp-integration"
}

# Cross-project IAM permissions for BigQuery Scheduler to access Tableau data
resource "google_project_iam_member" "bigquery_scheduler_tableau_access" {
  for_each = toset([
    "roles/bigquery.dataViewer",
    "roles/bigquery.jobUser"
  ])

  project = var.project_id
  role    = each.value
  member  = "serviceAccount:${var.bigquery_scheduler_service_account_email}"
}
