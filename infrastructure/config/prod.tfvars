# common

environment = "prod"

api_project_id = "ict-p-us-east1-api"

project_id = "ict-p-tableau"
region     = "us-east1"
zone       = "us-east1-b"

domain      = "bi.ict.dematic.cloud"
network_tag = "tableau-server"

supporting_resource_bucket_name = "ict-p-tableau-artifacts"

cost_center     = "550164"
resource_prefix = "tableau-server"

# network

app_subnet_cidr                  = "10.0.30.0/24"
serverless_connector_subnet_cidr = "10.124.0.0/28"

# cluster backend

cluster_node_image_family = "ubuntu-2204-lts"

# minimum requirements for Tableau for primary and additional nodes
primary_node_disk_size_gb = 128
primary_node_disk_type    = "pd-balanced"
primary_node_machine_type = "e2-highmem-8"

additional_node_count        = 1
additional_node_disk_size_gb = 128
additional_node_disk_type    = "pd-balanced"
additional_node_machine_type = "e2-custom-8-16384"

tableau_admin_password_secret = "tableau-server-admin-password"
tableau_admin_username        = "tableau-admin"
tableau_installer_bucket      = "ict-tableau-artifacts"
tableau_version               = "2023.3.6"

# management backend

artifact_registry_root       = "us-docker.pkg.dev"
artifact_registry_project    = "ict-o-registry"
artifact_registry_repository = "ict-o-registry"

cloud_run_image_name   = "ict-tableau-management-api"
cloud_run_service_name = "ict-tableau-management-api"
cloud_run_target_tag   = "stable"

auth_domain   = "control-tower-prod.us.auth0.com"
auth_audience = "https://api.ict.dematic.cloud"
